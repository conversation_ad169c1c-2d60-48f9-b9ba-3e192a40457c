/* IsoPur Custom Styles */

/* Icon Styles */
.lineal-fill { fill: #cf1818 !important; }
.lineal-stroke { fill: #343a40 !important; }
.lineal-stroke { fill: #ffffff50 !important; }

/* Mobile Navigation Fix */
@media (max-width: 991.98px) {
    .hamburger.offcanvas-nav-btn {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 1050 !important;
    }

    .navbar-other {
        display: flex !important;
        justify-content: flex-end !important;
    }
}

/* Product Card Enhancements */
.card.h-100 {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card.h-100:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

/* Category Gradient Styles */
.card.shadow-xl.lift.bg-gradient-category-blue {
    background: linear-gradient(135deg, #1d75bd 0, #0d3b5e 100%) !important;
    box-shadow: 0 10px 20px rgba(29,117,189,.3), 0 6px 6px rgba(29,117,189,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-blue:hover {
    box-shadow: 0 15px 30px rgba(29,117,189,.4), 0 10px 10px rgba(29,117,189,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-green {
    background: linear-gradient(135deg, #209d52 0, #0e4f29 100%) !important;
    box-shadow: 0 10px 20px rgba(32,157,82,.3), 0 6px 6px rgba(32,157,82,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-green:hover {
    box-shadow: 0 15px 30px rgba(32,157,82,.4), 0 10px 10px rgba(32,157,82,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-pink {
    background: linear-gradient(135deg, #e00787 0, #700343 100%) !important;
    box-shadow: 0 10px 20px rgba(224,7,135,.3), 0 6px 6px rgba(224,7,135,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-pink:hover {
    box-shadow: 0 15px 30px rgba(224,7,135,.4), 0 10px 10px rgba(224,7,135,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-light-green {
    background: linear-gradient(135deg, #6aa73e 0, #35531f 100%) !important;
    box-shadow: 0 10px 20px rgba(106,167,62,.3), 0 6px 6px rgba(106,167,62,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-light-green:hover {
    box-shadow: 0 15px 30px rgba(106,167,62,.4), 0 10px 10px rgba(106,167,62,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-teal {
    background: linear-gradient(135deg, #01b3ad 0, #005956 100%) !important;
    box-shadow: 0 10px 20px rgba(1,179,173,.3), 0 6px 6px rgba(1,179,173,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-teal:hover {
    box-shadow: 0 15px 30px rgba(1,179,173,.4), 0 10px 10px rgba(1,179,173,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-yellow {
    background: linear-gradient(135deg, #e7c745 0, #736322 100%) !important;
    box-shadow: 0 10px 20px rgba(231,199,69,.3), 0 6px 6px rgba(231,199,69,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-yellow:hover {
    box-shadow: 0 15px 30px rgba(231,199,69,.4), 0 10px 10px rgba(231,199,69,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-purple {
    background: linear-gradient(135deg, #5f1f71 0, #2f0f38 100%) !important;
    box-shadow: 0 10px 20px rgba(95,31,113,.3), 0 6px 6px rgba(95,31,113,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-purple:hover {
    box-shadow: 0 15px 30px rgba(95,31,113,.4), 0 10px 10px rgba(95,31,113,.2) !important;
    transform: translateY(-5px) !important;
}

.card.shadow-xl.lift.bg-gradient-category-orange {
    background: linear-gradient(135deg, #f47821 0, #7a3c10 100%) !important;
    box-shadow: 0 10px 20px rgba(244,120,33,.3), 0 6px 6px rgba(244,120,33,.2) !important;
}

.card.shadow-xl.lift.bg-gradient-category-orange:hover {
    box-shadow: 0 15px 30px rgba(244,120,33,.4), 0 10px 10px rgba(244,120,33,.2) !important;
    transform: translateY(-5px) !important;
}

/* Text Colors for Categories */
.text-primary { color: #1d75bd !important; }
.text-green { color: #209d52 !important; }
.text-pink { color: #e00787 !important; }
.text-leaf { color: #6aa73e !important; }
.text-teal { color: #01b3ad !important; }
.text-yellow { color: #e7c745 !important; }
.text-purple { color: #5f1f71 !important; }
.text-orange { color: #f47821 !important; }

/* Button Colors for Categories */
.btn-pink {
    background-color: #e00787;
    border-color: #e00787;
    color: white;
}
.btn-pink:hover {
    background-color: #c00670;
    border-color: #c00670;
}

.btn-leaf {
    background-color: #6aa73e;
    border-color: #6aa73e;
    color: white;
}
.btn-leaf:hover {
    background-color: #5a9034;
    border-color: #5a9034;
}

.btn-teal {
    background-color: #01b3ad;
    border-color: #01b3ad;
    color: white;
}
.btn-teal:hover {
    background-color: #019a95;
    border-color: #019a95;
}

.btn-yellow {
    background-color: #e7c745;
    border-color: #e7c745;
    color: #333;
}
.btn-yellow:hover {
    background-color: #d4b63c;
    border-color: #d4b63c;
}

.btn-purple {
    background-color: #5f1f71;
    border-color: #5f1f71;
    color: white;
}
.btn-purple:hover {
    background-color: #4f1a5f;
    border-color: #4f1a5f;
}

.btn-orange {
    background-color: #f47821;
    border-color: #f47821;
    color: white;
}
.btn-orange:hover {
    background-color: #e0691e;
    border-color: #e0691e;
}

/* Modern Card Design */
.modern-card {
    border-radius: 16px !important;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0,0,0,0.08) !important;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.modern-header {
    padding: 2rem !important;
    border-radius: 16px 16px 0 0 !important;
    position: relative;
    overflow: hidden;
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.modern-body {
    padding: 2rem !important;
    background: #fafbfc;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Ensure consistent subcategory grid heights */
.modern-body .row.gy-4 {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
}

.icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.icon-wrapper i {
    font-size: 28px;
    color: white;
}

.card-title {
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    color: white !important;
}

.card-subtitle {
    font-size: 0.95rem !important;
    font-weight: 400 !important;
}

.subcategory-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.06);
    transition: all 0.2s ease;
    height: 100%;
}

.subcategory-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: rgba(0,0,0,0.12);
}

.modern-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.modern-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.06);
    position: relative;
    padding-left: 1.5rem;
    font-size: 0.9rem;
    color: #6c757d;
    transition: color 0.2s ease;
}

.modern-list li:last-child {
    border-bottom: none;
}

.modern-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #007bff;
    font-weight: bold;
    font-size: 1.2rem;
}

.modern-list li:hover {
    color: #495057;
}

.modern-btn {
    padding: 0.75rem 2rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    text-transform: none !important;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.modern-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.25) !important;
}

/* Gradient Headers */
.bg-gradient-primary { background: linear-gradient(135deg, #1d75bd 0, #0d3b5e 100%) !important; }
.bg-gradient-green { background: linear-gradient(135deg, #209d52 0, #0e4f29 100%) !important; }
.bg-gradient-pink { background: linear-gradient(135deg, #e00787 0, #700343 100%) !important; }
.bg-gradient-light-green { background: linear-gradient(135deg, #6aa73e 0, #35531f 100%) !important; }
.bg-gradient-teal { background: linear-gradient(135deg, #01b3ad 0, #005956 100%) !important; }
.bg-gradient-yellow { background: linear-gradient(135deg, #e7c745 0, #736322 100%) !important; }
.bg-gradient-purple { background: linear-gradient(135deg, #5f1f71 0, #2f0f38 100%) !important; }
.bg-gradient-orange { background: linear-gradient(135deg, #f47821 0, #7a3c10 100%) !important; }