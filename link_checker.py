#!/usr/bin/env python3
"""
Link Checker für das IsoPur Website Projekt
Analysiert alle HTML-Dateien und überprüft interne und externe Links
"""

import os
import re
import glob
from urllib.parse import urljoin, urlparse
from pathlib import Path
import requests
from bs4 import BeautifulSoup
import json
from collections import defaultdict

class LinkChecker:
    def __init__(self, root_dir="."):
        self.root_dir = Path(root_dir)
        self.html_files = []
        self.all_links = defaultdict(list)  # file -> [links]
        self.broken_links = []
        self.fixed_links = []
        self.asset_files = set()
        self.external_links = set()
        
    def find_html_files(self):
        """Findet alle HTML-Dateien im Projekt"""
        patterns = ["*.html", "**/*.html"]
        for pattern in patterns:
            self.html_files.extend(self.root_dir.glob(pattern))
        print(f"Gefundene HTML-Dateien: {len(self.html_files)}")
        
    def find_asset_files(self):
        """Sammelt alle Asset-Dateien für Referenzprüfung"""
        asset_patterns = [
            "assets/**/*.*",
            "*.css", "*.js", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.svg", 
            "*.pdf", "*.webp", "*.ico", "*.woff", "*.woff2", "*.ttf", "*.eot"
        ]
        
        for pattern in asset_patterns:
            for file_path in self.root_dir.glob(pattern):
                if file_path.is_file():
                    # Relativer Pfad vom Root
                    rel_path = file_path.relative_to(self.root_dir)
                    self.asset_files.add(str(rel_path).replace('\\', '/'))
        
        print(f"Gefundene Asset-Dateien: {len(self.asset_files)}")
        
    def extract_links_from_html(self, html_file):
        """Extrahiert alle Links aus einer HTML-Datei"""
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            soup = BeautifulSoup(content, 'html.parser')
            links = []
            
            # href Links (a, link tags)
            for tag in soup.find_all(['a', 'link'], href=True):
                href = tag['href']
                if href and not href.startswith('#') and not href.startswith('mailto:'):
                    links.append({
                        'type': 'href',
                        'url': href,
                        'tag': tag.name,
                        'line': self._find_line_number(content, str(tag))
                    })
            
            # src Links (img, script, etc.)
            for tag in soup.find_all(['img', 'script'], src=True):
                src = tag['src']
                if src:
                    links.append({
                        'type': 'src',
                        'url': src,
                        'tag': tag.name,
                        'line': self._find_line_number(content, str(tag))
                    })
                    
            # srcset Links
            for tag in soup.find_all(srcset=True):
                srcset = tag['srcset']
                if srcset:
                    # Parse srcset (kann mehrere URLs enthalten)
                    srcset_urls = re.findall(r'([^\s,]+)', srcset)
                    for url in srcset_urls:
                        if not url.endswith('x'):  # Skip density descriptors like "2x"
                            links.append({
                                'type': 'srcset',
                                'url': url,
                                'tag': tag.name,
                                'line': self._find_line_number(content, str(tag))
                            })
            
            # data-image-src Links
            for tag in soup.find_all(attrs={'data-image-src': True}):
                data_src = tag['data-image-src']
                if data_src:
                    links.append({
                        'type': 'data-image-src',
                        'url': data_src,
                        'tag': tag.name,
                        'line': self._find_line_number(content, str(tag))
                    })
            
            rel_file = html_file.relative_to(self.root_dir)
            self.all_links[str(rel_file)] = links
            return links
            
        except Exception as e:
            print(f"Fehler beim Parsen von {html_file}: {e}")
            return []
    
    def _find_line_number(self, content, tag_str):
        """Findet die Zeilennummer eines Tags im HTML-Content"""
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if tag_str[:50] in line:  # Erste 50 Zeichen des Tags
                return i
        return 0
        
    def is_external_link(self, url):
        """Prüft ob ein Link extern ist"""
        return url.startswith(('http://', 'https://', '//'))
        
    def resolve_relative_path(self, base_file, relative_url):
        """Löst relative Pfade auf"""
        base_dir = Path(base_file).parent

        if relative_url.startswith('./'):
            relative_url = relative_url[2:]
            resolved = base_dir / relative_url
        elif relative_url.startswith('../'):
            # Gehe ein Verzeichnis nach oben
            parts = relative_url.split('/')
            up_count = 0
            for part in parts:
                if part == '..':
                    up_count += 1
                else:
                    break

            # Gehe entsprechend viele Verzeichnisse nach oben
            target_dir = base_dir
            for _ in range(up_count):
                target_dir = target_dir.parent

            # Füge den Rest des Pfads hinzu
            remaining_path = '/'.join(parts[up_count:])
            resolved = target_dir / remaining_path
        else:
            resolved = base_dir / relative_url

        return resolved
        
    def check_internal_link(self, base_file, url):
        """Prüft einen internen Link"""
        if url.startswith('/'):
            # Absoluter Pfad vom Root
            target_path = self.root_dir / url[1:]
        else:
            # Relativer Pfad
            target_path = self.resolve_relative_path(base_file, url)
            
        # Normalisiere den Pfad
        try:
            target_path = target_path.resolve()
            rel_target = target_path.relative_to(self.root_dir.resolve())
            target_str = str(rel_target).replace('\\', '/')
        except (ValueError, OSError):
            return False, f"Pfad außerhalb des Projekts: {target_path}"
            
        # Prüfe ob Datei existiert
        if target_path.exists():
            return True, None
            
        # Prüfe ob es ein Asset ist
        if target_str in self.asset_files:
            return True, None
            
        # Suche nach ähnlichen Dateien
        similar = self.find_similar_files(target_str)
        if similar:
            return False, f"Datei nicht gefunden. Ähnliche Dateien: {similar[:3]}"
        
        return False, f"Datei nicht gefunden: {target_str}"

    def find_similar_files(self, target_file):
        """Findet ähnliche Dateien basierend auf Dateinamen"""
        target_name = Path(target_file).name.lower()
        similar = []

        # Suche in allen Asset-Dateien
        for asset in self.asset_files:
            asset_name = Path(asset).name.lower()
            if self.levenshtein_distance(target_name, asset_name) <= 2:
                similar.append(asset)

        # Suche in HTML-Dateien
        for html_file in self.html_files:
            html_name = html_file.name.lower()
            if self.levenshtein_distance(target_name, html_name) <= 2:
                rel_path = html_file.relative_to(self.root_dir)
                similar.append(str(rel_path).replace('\\', '/'))

        return similar

    def levenshtein_distance(self, s1, s2):
        """Berechnet die Levenshtein-Distanz zwischen zwei Strings"""
        if len(s1) < len(s2):
            return self.levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    def check_external_link(self, url):
        """Prüft einen externen Link"""
        try:
            # Timeout für externe Links
            response = requests.head(url, timeout=10, allow_redirects=True)
            return response.status_code == 200, response.status_code
        except requests.RequestException as e:
            return False, str(e)

    def analyze_all_links(self):
        """Analysiert alle Links in allen HTML-Dateien"""
        print("Analysiere alle Links...")

        for html_file in self.html_files:
            print(f"Analysiere: {html_file.name}")
            links = self.extract_links_from_html(html_file)

            for link in links:
                url = link['url']

                if self.is_external_link(url):
                    self.external_links.add(url)
                else:
                    # Interner Link
                    is_valid, error = self.check_internal_link(html_file, url)
                    if not is_valid:
                        self.broken_links.append({
                            'file': str(html_file.relative_to(self.root_dir)),
                            'line': link['line'],
                            'type': link['type'],
                            'tag': link['tag'],
                            'url': url,
                            'error': error
                        })

    def suggest_fixes(self):
        """Schlägt Korrekturen für fehlerhafte Links vor"""
        fixes = []

        for broken in self.broken_links:
            url = broken['url']
            file_path = broken['file']

            # Versuche automatische Korrektur
            suggested_fix = self.auto_fix_link(file_path, url)
            if suggested_fix:
                fixes.append({
                    'original': broken,
                    'suggested_url': suggested_fix,
                    'confidence': 'high' if suggested_fix in self.asset_files else 'medium'
                })

        return fixes

    def auto_fix_link(self, base_file, broken_url):
        """Versucht automatische Korrektur eines Links"""
        # Häufige Fehler korrigieren

        # 1. Falsche relative Pfade (./assets vs ../assets)
        if broken_url.startswith('./assets/') and base_file.startswith('shop-products/'):
            # Shop-Produkte sind in Unterordner, brauchen ../
            return broken_url.replace('./', '../')

        # 2. Fehlende Dateiendungen
        if not Path(broken_url).suffix and not broken_url.endswith('/'):
            # Versuche .html hinzuzufügen
            html_candidate = broken_url + '.html'
            if self.check_internal_link(base_file, html_candidate)[0]:
                return html_candidate

        # 3. Groß-/Kleinschreibung
        target_name = Path(broken_url).name
        for asset in self.asset_files:
            if Path(asset).name.lower() == target_name.lower():
                # Ersetze nur den Dateinamen, behalte Pfad
                fixed_url = broken_url.replace(target_name, Path(asset).name)
                return fixed_url

        return None

    def generate_report(self):
        """Generiert einen Markdown-Bericht"""
        report = []
        report.append("# Link-Analyse Bericht - IsoPur Website")
        report.append("")
        report.append(f"**Analysierte HTML-Dateien:** {len(self.html_files)}")
        report.append(f"**Gefundene Asset-Dateien:** {len(self.asset_files)}")
        report.append(f"**Fehlerhafte interne Links:** {len(self.broken_links)}")
        report.append(f"**Externe Links:** {len(self.external_links)}")
        report.append("")

        if self.broken_links:
            report.append("## 🔴 Fehlerhafte Links")
            report.append("")
            report.append("| Datei | Zeile | Typ | URL | Fehler |")
            report.append("|-------|-------|-----|-----|--------|")

            for broken in self.broken_links:
                report.append(f"| {broken['file']} | {broken['line']} | {broken['type']} | `{broken['url']}` | {broken['error']} |")
            report.append("")

        # Vorgeschlagene Korrekturen
        fixes = self.suggest_fixes()
        if fixes:
            report.append("## 🔧 Vorgeschlagene Korrekturen")
            report.append("")
            report.append("| Original URL | Vorgeschlagene URL | Konfidenz |")
            report.append("|--------------|-------------------|-----------|")

            for fix in fixes:
                orig_url = fix['original']['url']
                sugg_url = fix['suggested_url']
                conf = fix['confidence']
                report.append(f"| `{orig_url}` | `{sugg_url}` | {conf} |")
            report.append("")

        # Externe Links
        if self.external_links:
            report.append("## 🌐 Externe Links (zu prüfen)")
            report.append("")
            for ext_link in sorted(self.external_links):
                report.append(f"- {ext_link}")
            report.append("")

        return "\n".join(report)

    def run_analysis(self):
        """Führt die komplette Analyse durch"""
        print("Starte Link-Analyse...")
        self.find_html_files()
        self.find_asset_files()
        self.analyze_all_links()

        # Generiere Bericht
        report = self.generate_report()

        # Speichere Bericht
        with open('link_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("Analyse abgeschlossen. Bericht gespeichert in: link_analysis_report.md")
        return report

if __name__ == "__main__":
    checker = LinkChecker()
    report = checker.run_analysis()
    print("\n" + "="*50)
    print(report)
