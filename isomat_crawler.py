#!/usr/bin/env python3
"""
ISOMAT Product Crawler
Crawlt alle Produkte von isomat.com.de und erstellt lokale HTML-Seiten
"""

import os
import re
import json
import requests
from pathlib import Path
from urllib.parse import urljoin, urlparse
import time
from bs4 import BeautifulSoup
import hashlib

class IsomatCrawler:
    def __init__(self, base_url="https://www.isomat.com.de", output_dir="products"):
        self.base_url = base_url
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Datenstrukturen
        self.products = []
        self.categories = {}
        self.downloaded_images = {}
        self.failed_urls = []
        
        # Session für bessere Performance
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def get_product_urls(self):
        """Sammelt alle Produkt-URLs von der Website"""
        print("Sammle Produkt-URLs...")
        
        # Bekannte Produkt-URLs aus der Map-Funktion
        product_urls = [
            "https://www.isomat.com.de/product/aquamat-2-de",
            "https://www.isomat.com.de/product/flex-pu-2k",
            "https://www.isomat.com.de/product/isoflex-pas-580",
            "https://www.isomat.com.de/product/topcoat-pu-710",
            "https://www.isomat.com.de/product/aquamat-monoelastic-ultra",
            "https://www.isomat.com.de/product/aquamat-active",
            # Weitere URLs werden dynamisch hinzugefügt
        ]
        
        # Versuche weitere URLs über Sitemap oder Kategorien zu finden
        try:
            # Crawle Hauptkategorien
            categories_response = self.session.get(f"{self.base_url}/products")
            if categories_response.status_code == 200:
                soup = BeautifulSoup(categories_response.content, 'html.parser')
                category_links = soup.find_all('a', href=re.compile(r'/products/'))
                
                for link in category_links:
                    href = link.get('href')
                    if href and href.startswith('/products/'):
                        category_url = urljoin(self.base_url, href)
                        print(f"Crawle Kategorie: {category_url}")
                        self.crawl_category_products(category_url, product_urls)
                        
        except Exception as e:
            print(f"Fehler beim Sammeln der URLs: {e}")
            
        # Entferne Duplikate und filtere nur deutsche Produktseiten
        unique_urls = list(set(product_urls))
        german_product_urls = [url for url in unique_urls if '/product/' in url and ('-de' in url or url.endswith('/product/'))]
        
        print(f"Gefunden: {len(german_product_urls)} deutsche Produkt-URLs")
        return german_product_urls[:50]  # Limitiere für Test auf 50 Produkte
        
    def crawl_category_products(self, category_url, product_urls):
        """Crawlt Produkte aus einer Kategorie"""
        try:
            response = self.session.get(category_url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Suche nach Produkt-Links
                product_links = soup.find_all('a', href=re.compile(r'/product/'))
                for link in product_links:
                    href = link.get('href')
                    if href:
                        full_url = urljoin(self.base_url, href)
                        if full_url not in product_urls:
                            product_urls.append(full_url)
                            
        except Exception as e:
            print(f"Fehler beim Crawlen der Kategorie {category_url}: {e}")
            
    def extract_product_data(self, url):
        """Extrahiert Produktdaten von einer URL"""
        print(f"Crawle Produkt: {url}")
        
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code != 200:
                print(f"Fehler {response.status_code} bei {url}")
                self.failed_urls.append(url)
                return None
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Prüfe auf 404-Seite
            if "We could not find the page" in response.text:
                print(f"404-Seite gefunden: {url}")
                self.failed_urls.append(url)
                return None
            
            # Extrahiere Produktdaten
            product_data = {
                'url': url,
                'slug': self.extract_slug(url),
                'name': self.extract_product_name(soup),
                'description': self.extract_description(soup),
                'hero_image': self.extract_hero_image(soup),
                'properties': self.extract_properties(soup),
                'color': self.extract_field(soup, 'FARBE'),
                'consumption': self.extract_field(soup, 'VERBRAUCH'),
                'delivery_form': self.extract_field(soup, 'LIEFERFORM'),
                'technical_sheets': self.extract_technical_sheets(soup),
                'certifications': self.extract_certifications(soup),
                'related_products': self.extract_related_products(soup)
            }
            
            # Validiere Daten
            if not product_data['name'] or not product_data['slug']:
                print(f"Unvollständige Daten für {url}")
                return None
                
            return product_data
            
        except Exception as e:
            print(f"Fehler beim Extrahieren von {url}: {e}")
            self.failed_urls.append(url)
            return None
            
    def extract_slug(self, url):
        """Extrahiert den Slug aus der URL"""
        # Beispiel: https://www.isomat.com.de/product/aquamat-2-de -> aquamat
        path = urlparse(url).path
        parts = path.strip('/').split('/')
        if len(parts) >= 2 and parts[0] == 'product':
            slug = parts[1]
            # Entferne Suffixe wie -2-de
            slug = re.sub(r'-\d+-[a-z]{2}$', '', slug)
            slug = re.sub(r'-[a-z]{2}$', '', slug)
            return slug
        return None
        
    def extract_product_name(self, soup):
        """Extrahiert den Produktnamen (H1)"""
        h1 = soup.find('h1', class_='product-title')
        if h1:
            return h1.get_text(strip=True)
        
        # Fallback
        h1 = soup.find('h1')
        if h1:
            return h1.get_text(strip=True)
        return None
        
    def extract_description(self, soup):
        """Extrahiert die Kurzbeschreibung"""
        excerpt = soup.find('p', class_='product-excerpt')
        if excerpt:
            text = excerpt.get_text(strip=True)
            # Limitiere auf 160 Zeichen
            return text[:160] + '...' if len(text) > 160 else text
        return None
        
    def extract_hero_image(self, soup):
        """Extrahiert das Hero-Bild"""
        # Suche nach dem Hauptproduktbild
        img_container = soup.find('div', class_='image-container')
        if img_container:
            img = img_container.find('img')
            if img and img.get('src'):
                return img['src']
                
        # Fallback: Erstes Produktbild
        product_images = soup.find_all('img', alt=True)
        for img in product_images:
            src = img.get('src', '')
            if src and any(ext in src.lower() for ext in ['.png', '.jpg', '.jpeg', '.webp']):
                if 'product' in src.lower() or img.get('alt', '').strip():
                    return src
        return None
        
    def extract_properties(self, soup):
        """Extrahiert Eigenschaften und Anwendungsgebiete"""
        properties_section = soup.find('h2', string=re.compile(r'EIGENSCHAFTEN.*ANWENDUNG', re.I))
        if properties_section:
            content_div = properties_section.find_next('div', class_='product-details-text')
            if content_div:
                return content_div.get_text(strip=True)
        return None
        
    def extract_field(self, soup, field_name):
        """Extrahiert ein spezifisches Feld (FARBE, VERBRAUCH, etc.)"""
        field_header = soup.find('h2', string=re.compile(field_name, re.I))
        if field_header:
            content_div = field_header.find_next('div', class_='product-details-text')
            if content_div:
                return content_div.get_text(strip=True)
        return None
        
    def extract_technical_sheets(self, soup):
        """Extrahiert Links zu technischen Merkblättern"""
        sheets = []
        
        # Suche nach PDF-Links
        pdf_links = soup.find_all('a', href=re.compile(r'\.pdf$', re.I))
        for link in pdf_links:
            href = link.get('href')
            text = link.get_text(strip=True)
            if href and text:
                sheets.append({
                    'title': text,
                    'url': urljoin(self.base_url, href) if href.startswith('/') else href
                })
                
        return sheets
        
    def extract_certifications(self, soup):
        """Extrahiert Zertifizierungsbilder"""
        certs = []
        cert_divs = soup.find_all('div', class_='certification')
        for div in cert_divs:
            img = div.find('img')
            if img and img.get('src'):
                certs.append(img['src'])
        return certs
        
    def extract_related_products(self, soup):
        """Extrahiert verwandte Produkte"""
        related = []
        related_section = soup.find('div', class_='related-products')
        if related_section:
            product_links = related_section.find_all('a', href=re.compile(r'/product/'))
            for link in product_links[:5]:  # Limitiere auf 5
                href = link.get('href')
                title_elem = link.find('h3')
                if href and title_elem:
                    related.append({
                        'name': title_elem.get_text(strip=True),
                        'url': urljoin(self.base_url, href) if href.startswith('/') else href
                    })
        return related

    def download_image(self, image_url, product_slug):
        """Lädt ein Bild herunter und speichert es lokal"""
        if not image_url or image_url in self.downloaded_images:
            return self.downloaded_images.get(image_url)

        try:
            # Erstelle lokalen Dateinamen
            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"{product_slug}_image.png"

            # Erstelle Verzeichnis
            img_dir = self.output_dir / "assets" / "img" / "products"
            img_dir.mkdir(parents=True, exist_ok=True)

            local_path = img_dir / filename

            # Lade Bild herunter
            if not local_path.exists():
                response = self.session.get(image_url, timeout=30)
                if response.status_code == 200:
                    with open(local_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Bild heruntergeladen: {filename}")
                else:
                    print(f"Fehler beim Herunterladen von {image_url}")
                    return None

            # Relativer Pfad für HTML
            relative_path = f"../assets/img/products/{filename}"
            self.downloaded_images[image_url] = relative_path
            return relative_path

        except Exception as e:
            print(f"Fehler beim Herunterladen von {image_url}: {e}")
            return None

    def generate_product_html(self, product_data):
        """Generiert HTML-Seite für ein Produkt"""
        template = f'''<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{product_data.get('description', '')}">
    <meta name="keywords" content="IsoPur, {product_data['name']}, Abdichtung, Bauprojekte">
    <meta name="author" content="IsoPur Team">
    <title>{product_data['name']} | IsoPur GmbH</title>

    <link rel="apple-touch-icon" sizes="180x180" href="../assets/img/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../assets/img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../assets/img/favicon-16x16.png">
    <link rel="icon" type="image/ico" sizes="original" href="../assets/img/favicon.ico">
    <link rel="manifest" href="../assets/img/site.webmanifest">
    <link rel="stylesheet" href="../assets/css/plugins.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/custom.css">
</head>

<body>
    <div class="content-wrapper">
        <!-- Header -->
        <header class="wrapper bg-dark">
            <nav class="navbar navbar-expand-lg center-nav navbar-dark navbar-bg-dark caret-none shadow-sm">
                <div class="container flex-lg-row flex-nowrap align-items-center">
                    <div class="navbar-brand w-100">
                        <a href="../index.html">
                            <img class="logo-light" src="../assets/img/logo-no_slogan.png"
                                 srcset="../assets/img/<EMAIL> 2x" alt="ISOPUR Logo"
                                 style="max-width: 180px; width: auto; height: auto;" />
                        </a>
                    </div>

                    <!-- Navigation -->
                    <div class="navbar-collapse offcanvas offcanvas-nav offcanvas-start">
                        <div class="offcanvas-body ms-lg-auto d-flex flex-column h-100">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a class="nav-link" href="../index.html">Home</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../index.html#products">Neuheiten & Angebote</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../catalog.html">Katalog</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../about.html">Über uns</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="navbar-other w-100 d-flex ms-auto">
                        <ul class="navbar-nav flex-row align-items-center ms-auto">
                            <li class="nav-item d-none d-md-block">
                                <a href="../contact.html" class="btn btn-sm btn-primary rounded-pill">Kontakt</a>
                            </li>
                            <li class="nav-item d-lg-none">
                                <button class="hamburger offcanvas-nav-btn"><span></span></button>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Produktinhalt -->
        <section class="wrapper bg-light">
            <div class="container py-14 py-md-16">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Produktdetails -->
                        <div class="product-single">
                            <div class="row gx-lg-8 gx-xl-12 gy-10 align-items-center">
                                <div class="col-lg-6">
                                    {self.generate_product_image_html(product_data)}
                                </div>
                                <div class="col-lg-6">
                                    <h1 class="display-4 mb-3">{product_data['name']}</h1>
                                    {f'<p class="lead mb-6">{product_data["description"]}</p>' if product_data.get('description') else ''}

                                    {self.generate_technical_sheets_html(product_data)}
                                </div>
                            </div>

                            {self.generate_product_details_html(product_data)}
                        </div>
                    </div>

                    <div class="col-lg-4">
                        {self.generate_sidebar_html()}
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-dark text-inverse">
            <div class="container py-13 py-md-15">
                <div class="row gy-6 gy-lg-0">
                    <div class="col-md-4 col-lg-3">
                        <div class="widget">
                            <img class="mb-4" height="auto" width="100%" style="transform: translateX(-25%);"
                                 src="../assets/img/logo.png" srcset="../assets/img/<EMAIL> 2x" alt="ISOPUR Logo" />
                            <p class="mb-4">© 2025 Webton e.U. <br class="d-none d-lg-block" />Alle Rechte vorbehalten.</p>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-4">
                        <div class="widget">
                            <h4 class="widget-title text-white mb-3">Standort & Kontakt</h4>
                            <address class="pe-xl-15 pe-xxl-17"><b>Landstraßer Hauptstraße 146/15/4</b><br />A-1030 Wien, Österreich</address>
                            <a href="mailto:<EMAIL>"><EMAIL></a><br /> 072 07 04 66 0
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-2">
                        <div class="widget">
                            <h4 class="widget-title text-white mb-3">Mehr Links</h4>
                            <ul class="list-unstyled mb-0">
                                <li><a href="../catalog.html">Produkt-Katalog</a></li>
                                <li><a href="../about.html">Über uns</a></li>
                                <li><a href="../contact.html">Kontakt</a></li>
                                <li><a href="../impressum.html">Impressum</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="../assets/js/plugins.js"></script>
    <script src="../assets/js/theme.js"></script>
</body>
</html>'''
        return template

    def generate_product_image_html(self, product_data):
        """Generiert HTML für Produktbild"""
        if product_data.get('hero_image'):
            local_image = self.download_image(product_data['hero_image'], product_data['slug'])
            if local_image:
                return f'''
                <figure class="rounded mb-6">
                    <img src="{local_image}" alt="{product_data['name']}" class="img-fluid" />
                </figure>
                '''
        return '<div class="placeholder-image bg-light rounded mb-6" style="height: 300px;"></div>'

    def generate_technical_sheets_html(self, product_data):
        """Generiert HTML für technische Merkblätter"""
        if not product_data.get('technical_sheets'):
            return ''

        html = '<div class="mb-6">'
        for sheet in product_data['technical_sheets']:
            html += f'''
            <a href="{sheet['url']}" target="_blank" class="btn btn-outline-primary rounded-pill me-2 mb-2">
                <i class="uil uil-download-alt me-1"></i>{sheet['title']}
            </a>
            '''
        html += '</div>'
        return html

    def generate_product_details_html(self, product_data):
        """Generiert HTML für Produktdetails"""
        html = '<div class="row mt-8">'

        # Eigenschaften
        if product_data.get('properties'):
            html += f'''
            <div class="col-md-12 mb-6">
                <h3 class="mb-3">Eigenschaften & Anwendungsgebiete</h3>
                <p>{product_data['properties']}</p>
            </div>
            '''

        # Weitere Felder
        fields = [
            ('color', 'Farbe'),
            ('consumption', 'Verbrauch'),
            ('delivery_form', 'Lieferform')
        ]

        for field_key, field_title in fields:
            if product_data.get(field_key):
                html += f'''
                <div class="col-md-4 mb-4">
                    <h4 class="mb-2">{field_title}</h4>
                    <p>{product_data[field_key]}</p>
                </div>
                '''

        html += '</div>'
        return html

    def generate_sidebar_html(self):
        """Generiert Sidebar mit Kategorien"""
        return '''
        <div class="sidebar">
            <div class="widget">
                <h4 class="widget-title mb-3">Produktkategorien</h4>
                <ul class="list-unstyled">
                    <li><a href="../products/bauwerksabdichtung.html">Bauwerksabdichtung</a></li>
                    <li><a href="../products/wdv-systeme.html">WDV-Systeme</a></li>
                    <li><a href="../products/farben-oberflachenschutz.html">Farben & Oberflächenschutz</a></li>
                    <li><a href="../products/fliesen-natursteinverlegung.html">Fliesen- & Natursteinverlegung</a></li>
                    <li><a href="../products/betonherstellung-instandsetzung.html">Betonherstellung & -instandsetzung</a></li>
                    <li><a href="../products/mauerwerksherstellung-instandsetzung.html">Mauerwerksherstellung & -instandsetzung</a></li>
                    <li><a href="../products/industriebeschichtungen.html">Industriebeschichtungen</a></li>
                    <li><a href="../products/dekorative-boden-wandbeschichtungen.html">Dekorative Boden- & Wandbeschichtungen</a></li>
                </ul>
            </div>
        </div>
        '''

    def run_crawler(self):
        """Führt den kompletten Crawling-Prozess durch"""
        print("Starte ISOMAT Crawler...")

        # 1. Sammle URLs
        product_urls = self.get_product_urls()

        # 2. Crawle Produkte
        for i, url in enumerate(product_urls, 1):
            print(f"Verarbeite {i}/{len(product_urls)}: {url}")

            product_data = self.extract_product_data(url)
            if product_data:
                self.products.append(product_data)

                # Generiere HTML-Seite
                html_content = self.generate_product_html(product_data)
                html_file = self.output_dir / f"{product_data['slug']}.html"

                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                print(f"HTML erstellt: {html_file}")

            # Pause zwischen Requests
            time.sleep(1)

        # 3. Erstelle JSON-Daten
        self.create_data_files()

        # 4. Erstelle Sitemap
        self.create_sitemap()

        # 5. Erstelle Bericht
        self.create_report()

        print(f"Crawler abgeschlossen. {len(self.products)} Produkte verarbeitet.")

    def create_data_files(self):
        """Erstellt JSON-Dateien mit Produktdaten"""
        # data.json für CMS
        data_json = {
            'products': self.products,
            'categories': self.categories,
            'meta': {
                'total_products': len(self.products),
                'failed_urls': len(self.failed_urls),
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }

        with open(self.output_dir / 'data.json', 'w', encoding='utf-8') as f:
            json.dump(data_json, f, indent=2, ensure_ascii=False)

        print("data.json erstellt")

    def create_sitemap(self):
        """Erstellt sitemap.json"""
        sitemap = {
            'products': [
                {
                    'slug': product['slug'],
                    'name': product['name'],
                    'url': f"/products/{product['slug']}.html",
                    'description': product.get('description', '')
                }
                for product in self.products
            ]
        }

        with open(self.output_dir / 'sitemap.json', 'w', encoding='utf-8') as f:
            json.dump(sitemap, f, indent=2, ensure_ascii=False)

        print("sitemap.json erstellt")

    def create_report(self):
        """Erstellt Abschlussbericht"""
        report = f"""# ISOMAT Crawler Bericht

## Zusammenfassung
- **Verarbeitete Produkte:** {len(self.products)}
- **Fehlgeschlagene URLs:** {len(self.failed_urls)}
- **Heruntergeladene Bilder:** {len(self.downloaded_images)}

## Erfolgreich verarbeitete Produkte
{chr(10).join([f"- {p['name']} ({p['slug']})" for p in self.products])}

## Fehlgeschlagene URLs
{chr(10).join([f"- {url}" for url in self.failed_urls])}

## Nächste Schritte
1. Prüfen Sie die generierten HTML-Dateien
2. Testen Sie alle Links und Bilder
3. Integrieren Sie die Produktseiten in die Hauptnavigation
4. Führen Sie einen Link-Check durch
"""

        with open(self.output_dir / 'crawler_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("Bericht erstellt: crawler_report.md")

if __name__ == "__main__":
    crawler = IsomatCrawler()
    crawler.run_crawler()
