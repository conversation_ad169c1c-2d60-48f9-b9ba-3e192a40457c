#!/usr/bin/env python3
"""
ISOMAT Produktseiten Generator
Erstellt lokale Produktseiten basierend auf isomat.com.de
"""

import os
import re
import json
import time
import requests
from pathlib import Path
from urllib.parse import urlparse

class ProductGenerator:
    def __init__(self):
        self.products_dir = Path("products")
        self.products_dir.mkdir(exist_ok=True)
        
        # Assets-Verzeichnis erstellen
        self.assets_dir = self.products_dir / "assets" / "img" / "products"
        self.assets_dir.mkdir(parents=True, exist_ok=True)
        
        self.products = []
        self.failed_urls = []
        
        # Test-URLs für erste Implementierung
        self.test_urls = [
            "https://www.isomat.com.de/product/aquamat-2-de",
            "https://www.isomat.com.de/product/flex-pu-2k",
            "https://www.isomat.com.de/product/isoflex-pas-580",
            "https://www.isomat.com.de/product/topcoat-pu-710",
            "https://www.isomat.com.de/product/aquamat-monoelastic-ultra"
        ]
        
    def extract_slug(self, url):
        """Extrahiert den Slug aus der URL"""
        path = urlparse(url).path
        parts = path.strip('/').split('/')
        if len(parts) >= 2 and parts[0] == 'product':
            slug = parts[1]
            # Entferne Suffixe wie -2-de
            slug = re.sub(r'-\d+-[a-z]{2}$', '', slug)
            slug = re.sub(r'-[a-z]{2}$', '', slug)
            return slug
        return None
        
    def download_image(self, image_url, product_slug):
        """Lädt ein Bild herunter und speichert es lokal"""
        if not image_url:
            return None
            
        try:
            # Erstelle lokalen Dateinamen
            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"{product_slug}_image.png"
                
            local_path = self.assets_dir / filename
            
            # Lade Bild herunter
            if not local_path.exists():
                response = requests.get(image_url, timeout=30)
                if response.status_code == 200:
                    with open(local_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Bild heruntergeladen: {filename}")
                else:
                    print(f"Fehler beim Herunterladen von {image_url}")
                    return None
                    
            # Relativer Pfad für HTML
            return f"../assets/img/products/{filename}"
            
        except Exception as e:
            print(f"Fehler beim Herunterladen von {image_url}: {e}")
            return None
            
    def generate_product_html(self, product_data):
        """Generiert HTML-Seite für ein Produkt"""
        
        # Lade Bild herunter
        local_image = None
        if product_data.get('hero_image'):
            local_image = self.download_image(product_data['hero_image'], product_data['slug'])
            
        # Generiere Bildbereich
        if local_image:
            image_html = f'''
            <figure class="rounded mb-6">
                <img src="{local_image}" alt="{product_data['name']}" class="img-fluid" />
            </figure>
            '''
        else:
            image_html = '''
            <div class="placeholder-image bg-light rounded mb-6 d-flex align-items-center justify-content-center" style="height: 300px; color: #666;">
                <span>Produktbild wird geladen...</span>
            </div>
            '''
            
        # Generiere technische Merkblätter
        tech_sheets_html = ''
        if product_data.get('technical_sheets'):
            tech_sheets_html = '<div class="mb-6">'
            for sheet in product_data['technical_sheets']:
                tech_sheets_html += f'''
                <a href="{sheet['url']}" target="_blank" class="btn btn-outline-primary rounded-pill me-2 mb-2">
                    <i class="uil uil-download-alt me-1"></i>{sheet['title']}
                </a>
                '''
            tech_sheets_html += '</div>'
            
        # Generiere Produktdetails
        details_html = ''
        if any([product_data.get('properties'), product_data.get('color'), product_data.get('consumption'), product_data.get('delivery_form')]):
            details_html = '<div class="row mt-8">'
            
            if product_data.get('properties'):
                details_html += f'''
                <div class="col-md-12 mb-6">
                    <h3 class="mb-3">Eigenschaften & Anwendungsgebiete</h3>
                    <p>{product_data['properties']}</p>
                </div>
                '''
                
            fields = [
                ('color', 'Farbe'),
                ('consumption', 'Verbrauch'), 
                ('delivery_form', 'Lieferform')
            ]
            
            for field_key, field_title in fields:
                if product_data.get(field_key):
                    details_html += f'''
                    <div class="col-md-4 mb-4">
                        <h4 class="mb-2">{field_title}</h4>
                        <p>{product_data[field_key]}</p>
                    </div>
                    '''
                    
            details_html += '</div>'
            
        # HTML Template
        template = f'''<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{product_data.get('description', '')}">
    <meta name="keywords" content="IsoPur, {product_data['name']}, Abdichtung, Bauprojekte">
    <meta name="author" content="IsoPur Team">
    <title>{product_data['name']} | IsoPur GmbH</title>
    
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/img/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../assets/img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../assets/img/favicon-16x16.png">
    <link rel="icon" type="image/ico" sizes="original" href="../assets/img/favicon.ico">
    <link rel="manifest" href="../assets/img/site.webmanifest">
    <link rel="stylesheet" href="../assets/css/plugins.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/custom.css">
</head>

<body>
    <div class="content-wrapper">
        <!-- Header -->
        <header class="wrapper bg-dark">
            <nav class="navbar navbar-expand-lg center-nav navbar-dark navbar-bg-dark caret-none shadow-sm">
                <div class="container flex-lg-row flex-nowrap align-items-center">
                    <div class="navbar-brand w-100">
                        <a href="../index.html">
                            <img class="logo-light" src="../assets/img/logo-no_slogan.png" 
                                 srcset="../assets/img/<EMAIL> 2x" alt="ISOPUR Logo" 
                                 style="max-width: 180px; width: auto; height: auto;" />
                        </a>
                    </div>
                    
                    <!-- Navigation -->
                    <div class="navbar-collapse offcanvas offcanvas-nav offcanvas-start">
                        <div class="offcanvas-body ms-lg-auto d-flex flex-column h-100">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a class="nav-link" href="../index.html">Home</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../index.html#products">Neuheiten & Angebote</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../catalog.html">Katalog</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../about.html">Über uns</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="navbar-other w-100 d-flex ms-auto">
                        <ul class="navbar-nav flex-row align-items-center ms-auto">
                            <li class="nav-item d-none d-md-block">
                                <a href="../contact.html" class="btn btn-sm btn-primary rounded-pill">Kontakt</a>
                            </li>
                            <li class="nav-item d-lg-none">
                                <button class="hamburger offcanvas-nav-btn"><span></span></button>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Produktinhalt -->
        <section class="wrapper bg-light">
            <div class="container py-14 py-md-16">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Produktdetails -->
                        <div class="product-single">
                            <div class="row gx-lg-8 gx-xl-12 gy-10 align-items-center">
                                <div class="col-lg-6">
                                    {image_html}
                                </div>
                                <div class="col-lg-6">
                                    <h1 class="display-4 mb-3">{product_data['name']}</h1>
                                    {f'<p class="lead mb-6">{product_data["description"]}</p>' if product_data.get('description') else ''}
                                    
                                    {tech_sheets_html}
                                </div>
                            </div>
                            
                            {details_html}
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="sidebar">
                            <div class="widget">
                                <h4 class="widget-title mb-3">Produktkategorien</h4>
                                <ul class="list-unstyled">
                                    <li><a href="bauwerksabdichtung.html">Bauwerksabdichtung</a></li>
                                    <li><a href="wdv-systeme.html">WDV-Systeme</a></li>
                                    <li><a href="farben-oberflachenschutz.html">Farben & Oberflächenschutz</a></li>
                                    <li><a href="fliesen-natursteinverlegung.html">Fliesen- & Natursteinverlegung</a></li>
                                    <li><a href="betonherstellung-instandsetzung.html">Betonherstellung & -instandsetzung</a></li>
                                    <li><a href="mauerwerksherstellung-instandsetzung.html">Mauerwerksherstellung & -instandsetzung</a></li>
                                    <li><a href="industriebeschichtungen.html">Industriebeschichtungen</a></li>
                                    <li><a href="dekorative-boden-wandbeschichtungen.html">Dekorative Boden- & Wandbeschichtungen</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-dark text-inverse">
            <div class="container py-13 py-md-15">
                <div class="row gy-6 gy-lg-0">
                    <div class="col-md-4 col-lg-3">
                        <div class="widget">
                            <img class="mb-4" height="auto" width="100%" style="transform: translateX(-25%);"
                                 src="../assets/img/logo.png" srcset="../assets/img/<EMAIL> 2x" alt="ISOPUR Logo" />
                            <p class="mb-4">© 2025 Webton e.U. <br class="d-none d-lg-block" />Alle Rechte vorbehalten.</p>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-4">
                        <div class="widget">
                            <h4 class="widget-title text-white mb-3">Standort & Kontakt</h4>
                            <address class="pe-xl-15 pe-xxl-17"><b>Landstraßer Hauptstraße 146/15/4</b><br />A-1030 Wien, Österreich</address>
                            <a href="mailto:<EMAIL>"><EMAIL></a><br /> 072 07 04 66 0
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-2">
                        <div class="widget">
                            <h4 class="widget-title text-white mb-3">Mehr Links</h4>
                            <ul class="list-unstyled mb-0">
                                <li><a href="../catalog.html">Produkt-Katalog</a></li>
                                <li><a href="../about.html">Über uns</a></li>
                                <li><a href="../contact.html">Kontakt</a></li>
                                <li><a href="../impressum.html">Impressum</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="../assets/js/plugins.js"></script>
    <script src="../assets/js/theme.js"></script>
</body>
</html>'''
        return template
