#!/usr/bin/env python3
"""
ISOMAT Product Crawler mit Firecrawl API
Bessere Extraktion durch strukturierte Datenextraktion
"""

import os
import re
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
import requests

class IsomatFirecrawler:
    def __init__(self, output_dir="products"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Datenstrukturen
        self.products = []
        self.failed_urls = []
        self.downloaded_images = {}
        
        # Bekannte Produkt-URLs (aus der Map-Funktion)
        self.product_urls = [
            "https://www.isomat.com.de/product/aquamat-2-de",
            "https://www.isomat.com.de/product/flex-pu-2k",
            "https://www.isomat.com.de/product/isoflex-pas-580",
            "https://www.isomat.com.de/product/topcoat-pu-710",
            "https://www.isomat.com.de/product/aquamat-monoelastic-ultra",
            "https://www.isomat.com.de/product/aquamat-active",
            "https://www.isomat.com.de/product/aquamat-elastic",
            "https://www.isomat.com.de/product/aquamat-foundation",
            "https://www.isomat.com.de/product/aquamat-primer",
            "https://www.isomat.com.de/product/aquamat-rapid",
            "https://www.isomat.com.de/product/aquamat-roof",
            "https://www.isomat.com.de/product/aquamat-standard",
            "https://www.isomat.com.de/product/aquamat-thixo",
            "https://www.isomat.com.de/product/aquamat-ultra",
            "https://www.isomat.com.de/product/flex-ms-1k",
            "https://www.isomat.com.de/product/flex-pu-1k",
            "https://www.isomat.com.de/product/isoflex-pu-500",
            "https://www.isomat.com.de/product/isoflex-pu-560",
            "https://www.isomat.com.de/product/multiflex-pu-50",
            "https://www.isomat.com.de/product/multiflex-pu-55"
        ]
        
    def extract_product_with_firecrawl(self, url):
        """Extrahiert Produktdaten mit Firecrawl API"""
        print(f"Crawle mit Firecrawl: {url}")
        
        try:
            # Verwende die Firecrawl-Funktion direkt
            from firecrawl import firecrawl_scrape_firecrawl
            
            # Schema für strukturierte Extraktion
            extraction_schema = {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Produktname aus H1 oder Titel"
                    },
                    "description": {
                        "type": "string", 
                        "description": "Kurze Produktbeschreibung, maximal 160 Zeichen"
                    },
                    "hero_image": {
                        "type": "string",
                        "description": "URL des Hauptproduktbildes"
                    },
                    "properties": {
                        "type": "string",
                        "description": "Eigenschaften und Anwendungsgebiete des Produkts"
                    },
                    "color": {
                        "type": "string",
                        "description": "Verfügbare Farben"
                    },
                    "consumption": {
                        "type": "string", 
                        "description": "Verbrauchsangaben"
                    },
                    "delivery_form": {
                        "type": "string",
                        "description": "Lieferform und Verpackung"
                    },
                    "technical_sheets": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "url": {"type": "string"}
                            }
                        },
                        "description": "Links zu technischen Merkblättern (PDFs)"
                    }
                },
                "required": ["name"]
            }
            
            # Extrahiere strukturierte Daten
            result = firecrawl_scrape_firecrawl(
                url=url,
                formats=["extract", "markdown"],
                extract={
                    "schema": extraction_schema,
                    "prompt": "Extrahiere alle Produktinformationen von dieser ISOMAT Produktseite. Achte besonders auf technische Details, Eigenschaften und Anwendungsgebiete."
                },
                onlyMainContent=True
            )
            
            if result and 'extract' in result:
                extracted_data = result['extract']
                
                # Ergänze fehlende Daten
                product_data = {
                    'url': url,
                    'slug': self.extract_slug(url),
                    'name': extracted_data.get('name', ''),
                    'description': extracted_data.get('description', ''),
                    'hero_image': extracted_data.get('hero_image', ''),
                    'properties': extracted_data.get('properties', ''),
                    'color': extracted_data.get('color', ''),
                    'consumption': extracted_data.get('consumption', ''),
                    'delivery_form': extracted_data.get('delivery_form', ''),
                    'technical_sheets': extracted_data.get('technical_sheets', []),
                    'markdown_content': result.get('markdown', '')
                }
                
                # Fallback-Extraktion aus Markdown wenn strukturierte Daten fehlen
                if not product_data['name'] and result.get('markdown'):
                    product_data = self.extract_from_markdown(result['markdown'], product_data)
                
                return product_data
                
        except Exception as e:
            print(f"Fehler bei Firecrawl-Extraktion von {url}: {e}")
            self.failed_urls.append(url)
            return None
            
    def extract_slug(self, url):
        """Extrahiert den Slug aus der URL"""
        path = urlparse(url).path
        parts = path.strip('/').split('/')
        if len(parts) >= 2 and parts[0] == 'product':
            slug = parts[1]
            # Entferne Suffixe wie -2-de
            slug = re.sub(r'-\d+-[a-z]{2}$', '', slug)
            slug = re.sub(r'-[a-z]{2}$', '', slug)
            return slug
        return None
        
    def extract_from_markdown(self, markdown_content, product_data):
        """Fallback-Extraktion aus Markdown-Content"""
        lines = markdown_content.split('\n')
        
        # Suche nach H1 für Produktname
        for line in lines:
            if line.startswith('# ') and not product_data['name']:
                product_data['name'] = line[2:].strip()
                break
                
        # Suche nach Bildern
        if not product_data['hero_image']:
            img_matches = re.findall(r'!\[.*?\]\((.*?)\)', markdown_content)
            for img_url in img_matches:
                if any(ext in img_url.lower() for ext in ['.png', '.jpg', '.jpeg', '.webp']):
                    product_data['hero_image'] = img_url
                    break
                    
        # Suche nach PDF-Links
        if not product_data['technical_sheets']:
            pdf_matches = re.findall(r'\[([^\]]+)\]\(([^)]+\.pdf)\)', markdown_content)
            product_data['technical_sheets'] = [
                {'title': title, 'url': url} for title, url in pdf_matches
            ]
            
        return product_data
        
    def download_image(self, image_url, product_slug):
        """Lädt ein Bild herunter und speichert es lokal"""
        if not image_url or image_url in self.downloaded_images:
            return self.downloaded_images.get(image_url)
            
        try:
            # Erstelle lokalen Dateinamen
            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"{product_slug}_image.png"
                
            # Erstelle Verzeichnis
            img_dir = self.output_dir / "assets" / "img" / "products"
            img_dir.mkdir(parents=True, exist_ok=True)
            
            local_path = img_dir / filename
            
            # Lade Bild herunter
            if not local_path.exists():
                response = requests.get(image_url, timeout=30)
                if response.status_code == 200:
                    with open(local_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Bild heruntergeladen: {filename}")
                else:
                    print(f"Fehler beim Herunterladen von {image_url}")
                    return None
                    
            # Relativer Pfad für HTML
            relative_path = f"../assets/img/products/{filename}"
            self.downloaded_images[image_url] = relative_path
            return relative_path
            
        except Exception as e:
            print(f"Fehler beim Herunterladen von {image_url}: {e}")
            return None
            
    def generate_product_html(self, product_data):
        """Generiert HTML-Seite für ein Produkt"""
        # Lade Bild herunter
        local_image = None
        if product_data.get('hero_image'):
            local_image = self.download_image(product_data['hero_image'], product_data['slug'])
            
        # Generiere Bildbereich
        image_html = ''
        if local_image:
            image_html = f'''
            <figure class="rounded mb-6">
                <img src="{local_image}" alt="{product_data['name']}" class="img-fluid" />
            </figure>
            '''
        else:
            image_html = '<div class="placeholder-image bg-light rounded mb-6" style="height: 300px; display: flex; align-items: center; justify-content: center; color: #666;">Produktbild wird geladen...</div>'
            
        # Generiere technische Merkblätter
        tech_sheets_html = ''
        if product_data.get('technical_sheets'):
            tech_sheets_html = '<div class="mb-6">'
            for sheet in product_data['technical_sheets']:
                tech_sheets_html += f'''
                <a href="{sheet['url']}" target="_blank" class="btn btn-outline-primary rounded-pill me-2 mb-2">
                    <i class="uil uil-download-alt me-1"></i>{sheet['title']}
                </a>
                '''
            tech_sheets_html += '</div>'
            
        # Generiere Produktdetails
        details_html = ''
        if any([product_data.get('properties'), product_data.get('color'), product_data.get('consumption'), product_data.get('delivery_form')]):
            details_html = '<div class="row mt-8">'
            
            if product_data.get('properties'):
                details_html += f'''
                <div class="col-md-12 mb-6">
                    <h3 class="mb-3">Eigenschaften & Anwendungsgebiete</h3>
                    <p>{product_data['properties']}</p>
                </div>
                '''
                
            fields = [
                ('color', 'Farbe'),
                ('consumption', 'Verbrauch'), 
                ('delivery_form', 'Lieferform')
            ]
            
            for field_key, field_title in fields:
                if product_data.get(field_key):
                    details_html += f'''
                    <div class="col-md-4 mb-4">
                        <h4 class="mb-2">{field_title}</h4>
                        <p>{product_data[field_key]}</p>
                    </div>
                    '''
                    
            details_html += '</div>'

        # HTML Template
        template = f'''<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{product_data.get('description', '')}">
    <meta name="keywords" content="IsoPur, {product_data['name']}, Abdichtung, Bauprojekte">
    <meta name="author" content="IsoPur Team">
    <title>{product_data['name']} | IsoPur GmbH</title>

    <link rel="apple-touch-icon" sizes="180x180" href="../assets/img/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../assets/img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../assets/img/favicon-16x16.png">
    <link rel="icon" type="image/ico" sizes="original" href="../assets/img/favicon.ico">
    <link rel="manifest" href="../assets/img/site.webmanifest">
    <link rel="stylesheet" href="../assets/css/plugins.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/custom.css">
</head>

<body>
    <div class="content-wrapper">
        <!-- Header -->
        <header class="wrapper bg-dark">
            <nav class="navbar navbar-expand-lg center-nav navbar-dark navbar-bg-dark caret-none shadow-sm">
                <div class="container flex-lg-row flex-nowrap align-items-center">
                    <div class="navbar-brand w-100">
                        <a href="../index.html">
                            <img class="logo-light" src="../assets/img/logo-no_slogan.png"
                                 srcset="../assets/img/<EMAIL> 2x" alt="ISOPUR Logo"
                                 style="max-width: 180px; width: auto; height: auto;" />
                        </a>
                    </div>

                    <!-- Navigation -->
                    <div class="navbar-collapse offcanvas offcanvas-nav offcanvas-start">
                        <div class="offcanvas-body ms-lg-auto d-flex flex-column h-100">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a class="nav-link" href="../index.html">Home</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../index.html#products">Neuheiten & Angebote</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../catalog.html">Katalog</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="../about.html">Über uns</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="navbar-other w-100 d-flex ms-auto">
                        <ul class="navbar-nav flex-row align-items-center ms-auto">
                            <li class="nav-item d-none d-md-block">
                                <a href="../contact.html" class="btn btn-sm btn-primary rounded-pill">Kontakt</a>
                            </li>
                            <li class="nav-item d-lg-none">
                                <button class="hamburger offcanvas-nav-btn"><span></span></button>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Produktinhalt -->
        <section class="wrapper bg-light">
            <div class="container py-14 py-md-16">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Produktdetails -->
                        <div class="product-single">
                            <div class="row gx-lg-8 gx-xl-12 gy-10 align-items-center">
                                <div class="col-lg-6">
                                    {image_html}
                                </div>
                                <div class="col-lg-6">
                                    <h1 class="display-4 mb-3">{product_data['name']}</h1>
                                    {f'<p class="lead mb-6">{product_data["description"]}</p>' if product_data.get('description') else ''}

                                    {tech_sheets_html}
                                </div>
                            </div>

                            {details_html}
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="sidebar">
                            <div class="widget">
                                <h4 class="widget-title mb-3">Produktkategorien</h4>
                                <ul class="list-unstyled">
                                    <li><a href="../products/bauwerksabdichtung.html">Bauwerksabdichtung</a></li>
                                    <li><a href="../products/wdv-systeme.html">WDV-Systeme</a></li>
                                    <li><a href="../products/farben-oberflachenschutz.html">Farben & Oberflächenschutz</a></li>
                                    <li><a href="../products/fliesen-natursteinverlegung.html">Fliesen- & Natursteinverlegung</a></li>
                                    <li><a href="../products/betonherstellung-instandsetzung.html">Betonherstellung & -instandsetzung</a></li>
                                    <li><a href="../products/mauerwerksherstellung-instandsetzung.html">Mauerwerksherstellung & -instandsetzung</a></li>
                                    <li><a href="../products/industriebeschichtungen.html">Industriebeschichtungen</a></li>
                                    <li><a href="../products/dekorative-boden-wandbeschichtungen.html">Dekorative Boden- & Wandbeschichtungen</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-dark text-inverse">
            <div class="container py-13 py-md-15">
                <div class="row gy-6 gy-lg-0">
                    <div class="col-md-4 col-lg-3">
                        <div class="widget">
                            <img class="mb-4" height="auto" width="100%" style="transform: translateX(-25%);"
                                 src="../assets/img/logo.png" srcset="../assets/img/<EMAIL> 2x" alt="ISOPUR Logo" />
                            <p class="mb-4">© 2025 Webton e.U. <br class="d-none d-lg-block" />Alle Rechte vorbehalten.</p>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-4">
                        <div class="widget">
                            <h4 class="widget-title text-white mb-3">Standort & Kontakt</h4>
                            <address class="pe-xl-15 pe-xxl-17"><b>Landstraßer Hauptstraße 146/15/4</b><br />A-1030 Wien, Österreich</address>
                            <a href="mailto:<EMAIL>"><EMAIL></a><br /> 072 07 04 66 0
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-2">
                        <div class="widget">
                            <h4 class="widget-title text-white mb-3">Mehr Links</h4>
                            <ul class="list-unstyled mb-0">
                                <li><a href="../catalog.html">Produkt-Katalog</a></li>
                                <li><a href="../about.html">Über uns</a></li>
                                <li><a href="../contact.html">Kontakt</a></li>
                                <li><a href="../impressum.html">Impressum</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="../assets/js/plugins.js"></script>
    <script src="../assets/js/theme.js"></script>
</body>
</html>'''
        return template

    def run_crawler(self):
        """Führt den kompletten Crawling-Prozess durch"""
        print("Starte ISOMAT Firecrawler...")

        # Limitiere auf erste 10 URLs für Test
        test_urls = self.product_urls[:10]

        for i, url in enumerate(test_urls, 1):
            print(f"Verarbeite {i}/{len(test_urls)}: {url}")

            product_data = self.extract_product_with_firecrawl(url)
            if product_data and product_data.get('name'):
                self.products.append(product_data)

                # Generiere HTML-Seite
                html_content = self.generate_product_html(product_data)
                html_file = self.output_dir / f"{product_data['slug']}.html"

                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                print(f"HTML erstellt: {html_file}")

            # Pause zwischen Requests
            time.sleep(2)

        # Erstelle JSON-Daten
        self.create_data_files()

        # Erstelle Sitemap
        self.create_sitemap()

        # Erstelle Bericht
        self.create_report()

        print(f"Crawler abgeschlossen. {len(self.products)} Produkte verarbeitet.")

    def create_data_files(self):
        """Erstellt JSON-Dateien mit Produktdaten"""
        data_json = {
            'products': self.products,
            'meta': {
                'total_products': len(self.products),
                'failed_urls': len(self.failed_urls),
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }

        with open(self.output_dir / 'data.json', 'w', encoding='utf-8') as f:
            json.dump(data_json, f, indent=2, ensure_ascii=False)

        print("data.json erstellt")

    def create_sitemap(self):
        """Erstellt sitemap.json"""
        sitemap = {
            'products': [
                {
                    'slug': product['slug'],
                    'name': product['name'],
                    'url': f"/products/{product['slug']}.html",
                    'description': product.get('description', '')
                }
                for product in self.products
            ]
        }

        with open(self.output_dir / 'sitemap.json', 'w', encoding='utf-8') as f:
            json.dump(sitemap, f, indent=2, ensure_ascii=False)

        print("sitemap.json erstellt")

    def create_report(self):
        """Erstellt Abschlussbericht"""
        report = f"""# ISOMAT Firecrawler Bericht

## Zusammenfassung
- **Verarbeitete Produkte:** {len(self.products)}
- **Fehlgeschlagene URLs:** {len(self.failed_urls)}
- **Heruntergeladene Bilder:** {len(self.downloaded_images)}

## Erfolgreich verarbeitete Produkte
{chr(10).join([f"- {p['name']} ({p['slug']})" for p in self.products])}

## Fehlgeschlagene URLs
{chr(10).join([f"- {url}" for url in self.failed_urls])}

## Nächste Schritte
1. Prüfen Sie die generierten HTML-Dateien
2. Testen Sie alle Links und Bilder
3. Integrieren Sie die Produktseiten in die Hauptnavigation
4. Führen Sie einen Link-Check durch
"""

        with open(self.output_dir / 'crawler_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("Bericht erstellt: crawler_report.md")

if __name__ == "__main__":
    crawler = IsomatFirecrawler()
    crawler.run_crawler()
