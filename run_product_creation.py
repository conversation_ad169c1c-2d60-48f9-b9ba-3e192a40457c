#!/usr/bin/env python3
"""
Führt die Produkterstellung mit Firecrawl aus
"""

import json
import time
import re
from pathlib import Path
from urllib.parse import urlparse
from create_products import ProductGenerator

# Schema für strukturierte Extraktion
EXTRACTION_SCHEMA = {
    "type": "object",
    "properties": {
        "name": {
            "type": "string",
            "description": "Produktname aus H1 oder Titel"
        },
        "description": {
            "type": "string", 
            "description": "Kurze Produktbeschreibung, maximal 160 Zeichen"
        },
        "hero_image": {
            "type": "string",
            "description": "URL des Hauptproduktbildes"
        },
        "properties": {
            "type": "string",
            "description": "Eigenschaften und Anwendungsgebiete des Produkts"
        },
        "color": {
            "type": "string",
            "description": "Verfügbare Farben"
        },
        "consumption": {
            "type": "string", 
            "description": "Verbrauchsangaben"
        },
        "delivery_form": {
            "type": "string",
            "description": "Lieferform und Verpackung"
        },
        "technical_sheets": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "url": {"type": "string"}
                }
            },
            "description": "Links zu technischen Merkblättern (PDFs)"
        }
    },
    "required": ["name"]
}

def extract_product_data(url):
    """Extrahiert Produktdaten mit Firecrawl"""
    print(f"Extrahiere Daten von: {url}")
    
    try:
        # Verwende die Firecrawl-Funktion
        result = firecrawl_scrape_firecrawl(
            url=url,
            formats=["extract"],
            extract={
                "schema": EXTRACTION_SCHEMA,
                "prompt": "Extrahiere alle Produktinformationen von dieser ISOMAT Produktseite. Achte besonders auf technische Details, Eigenschaften und Anwendungsgebiete."
            },
            onlyMainContent=True
        )
        
        if result and isinstance(result, dict):
            extracted_data = result
            
            # Ergänze fehlende Daten
            product_data = {
                'url': url,
                'slug': extract_slug(url),
                'name': extracted_data.get('name', ''),
                'description': extracted_data.get('description', ''),
                'hero_image': extracted_data.get('hero_image', ''),
                'properties': extracted_data.get('properties', ''),
                'color': extracted_data.get('color', ''),
                'consumption': extracted_data.get('consumption', ''),
                'delivery_form': extracted_data.get('delivery_form', ''),
                'technical_sheets': extracted_data.get('technical_sheets', [])
            }
            
            return product_data
            
    except Exception as e:
        print(f"Fehler bei der Extraktion von {url}: {e}")
        return None

def extract_slug(url):
    """Extrahiert den Slug aus der URL"""
    path = urlparse(url).path
    parts = path.strip('/').split('/')
    if len(parts) >= 2 and parts[0] == 'product':
        slug = parts[1]
        # Entferne Suffixe wie -2-de
        slug = re.sub(r'-\d+-[a-z]{2}$', '', slug)
        slug = re.sub(r'-[a-z]{2}$', '', slug)
        return slug
    return None

def main():
    """Hauptfunktion"""
    print("Starte Produkterstellung...")
    
    # Test-URLs
    test_urls = [
        "https://www.isomat.com.de/product/aquamat-2-de",
        "https://www.isomat.com.de/product/flex-pu-2k",
        "https://www.isomat.com.de/product/isoflex-pas-580",
        "https://www.isomat.com.de/product/topcoat-pu-710",
        "https://www.isomat.com.de/product/aquamat-monoelastic-ultra"
    ]
    
    generator = ProductGenerator()
    products = []
    failed_urls = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"\nVerarbeite {i}/{len(test_urls)}: {url}")
        
        # Extrahiere Produktdaten
        product_data = extract_product_data(url)
        
        if product_data and product_data.get('name'):
            products.append(product_data)
            
            # Generiere HTML-Seite
            html_content = generator.generate_product_html(product_data)
            html_file = generator.products_dir / f"{product_data['slug']}.html"
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            print(f"✅ HTML erstellt: {html_file}")
            print(f"   Produktname: {product_data['name']}")
            print(f"   Slug: {product_data['slug']}")
            
        else:
            failed_urls.append(url)
            print(f"❌ Fehler bei: {url}")
            
        # Pause zwischen Requests
        time.sleep(2)
    
    # Erstelle JSON-Daten
    create_data_files(products, failed_urls, generator.products_dir)
    
    # Erstelle Sitemap
    create_sitemap(products, generator.products_dir)
    
    # Erstelle Bericht
    create_report(products, failed_urls, generator.products_dir)
    
    print(f"\n🎉 Produkterstellung abgeschlossen!")
    print(f"   Erfolgreich: {len(products)} Produkte")
    print(f"   Fehlgeschlagen: {len(failed_urls)} URLs")

def create_data_files(products, failed_urls, output_dir):
    """Erstellt JSON-Dateien mit Produktdaten"""
    data_json = {
        'products': products,
        'meta': {
            'total_products': len(products),
            'failed_urls': len(failed_urls),
            'created_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    }
    
    with open(output_dir / 'data.json', 'w', encoding='utf-8') as f:
        json.dump(data_json, f, indent=2, ensure_ascii=False)
        
    print("📄 data.json erstellt")

def create_sitemap(products, output_dir):
    """Erstellt sitemap.json"""
    sitemap = {
        'products': [
            {
                'slug': product['slug'],
                'name': product['name'],
                'url': f"/products/{product['slug']}.html",
                'description': product.get('description', '')
            }
            for product in products
        ]
    }
    
    with open(output_dir / 'sitemap.json', 'w', encoding='utf-8') as f:
        json.dump(sitemap, f, indent=2, ensure_ascii=False)
        
    print("🗺️ sitemap.json erstellt")

def create_report(products, failed_urls, output_dir):
    """Erstellt Abschlussbericht"""
    report = f"""# ISOMAT Produkterstellung Bericht

## 📊 Zusammenfassung
- **Verarbeitete Produkte:** {len(products)}
- **Fehlgeschlagene URLs:** {len(failed_urls)}
- **Erstellt am:** {time.strftime('%Y-%m-%d %H:%M:%S')}

## ✅ Erfolgreich verarbeitete Produkte
{chr(10).join([f"- **{p['name']}** ({p['slug']}.html)" for p in products])}

## ❌ Fehlgeschlagene URLs
{chr(10).join([f"- {url}" for url in failed_urls]) if failed_urls else "Keine fehlgeschlagenen URLs"}

## 📁 Erstellte Dateien
- **HTML-Seiten:** {len(products)} Dateien im /products/ Verzeichnis
- **JSON-Daten:** data.json mit allen Produktinformationen
- **Sitemap:** sitemap.json für Navigation
- **Bilder:** Produktbilder in /products/assets/img/products/

## 🔗 Nächste Schritte
1. **Link-Check durchführen:** Alle Links und Bilder testen
2. **Navigation integrieren:** Produktseiten in Hauptnavigation einbinden
3. **Kategorieseiten erstellen:** Übersichtsseiten für Produktkategorien
4. **SEO optimieren:** Meta-Tags und strukturierte Daten ergänzen
5. **Responsive Design testen:** Mobile Darstellung prüfen

## 🎯 Qualitätskontrolle
- Alle Produktseiten verwenden das einheitliche Template
- Bilder werden lokal gespeichert für bessere Performance
- Technische Merkblätter verlinken auf Original-PDFs
- Sidebar mit Kategorien ist konsistent
"""
    
    with open(output_dir / 'creation_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
        
    print("📋 Bericht erstellt: creation_report.md")

if __name__ == "__main__":
    main()
